"""Challenge Creator Agent - Generates and enhances programming challenges."""

import uuid
import json
from typing import Dict, Any, Optional
from datetime import datetime

from models.challenge import Challenge, DifficultyLevel, ChallengeType, Solution
from models.message import Message, MessageType, AgentType
from core.llm_client import llm_client
from core.message_system import message_bus, MessageFactory
from core.logger import logger

class ChallengeCreatorAgent:
    """Agent responsible for creating and enhancing programming challenges."""
    
    def __init__(self):
        self.agent_type = AgentType.CHALLENGE_CREATOR
        self.current_challenge: Optional[Challenge] = None
        self.challenge_history = []
        
        # Subscribe to message bus
        message_bus.subscribe(self.agent_type, self.handle_message)
    
    async def handle_message(self, message: Message):
        """Handle incoming messages."""
        try:
            if message.type == MessageType.CHALLENGE_REQUEST:
                await self._handle_challenge_request(message)
            elif message.type == MessageType.ENHANCEMENT_REQUEST:
                await self._handle_enhancement_request(message)
            else:
                logger.log_error(f"Unknown message type: {message.type}", str(self.agent_type))
        except Exception as e:
            logger.log_error(f"Error handling message: {str(e)}", str(self.agent_type))
            
            # Send error response
            error_msg = MessageFactory.create_error_message(
                self.agent_type, message.sender, str(e)
            )
            await message_bus.publish(error_msg)
    
    async def _handle_challenge_request(self, message: Message):
        """Handle request to create a new challenge."""
        challenge_type = message.content.get("challenge_type")
        difficulty = message.content.get("difficulty", "beginner")
        
        challenge = await self.create_initial_challenge(challenge_type, difficulty)
        
        # Send challenge response
        response = MessageFactory.create_challenge_response(
            self.agent_type, message.sender, challenge.model_dump()
        )
        await message_bus.publish(response)
    
    async def _handle_enhancement_request(self, message: Message):
        """Handle request to enhance a challenge based on a solution."""
        challenge_id = message.content.get("challenge_id")
        solution_data = message.content.get("solution")
        
        # Find the original challenge
        original_challenge = None
        for challenge in self.challenge_history:
            if challenge.id == challenge_id:
                original_challenge = challenge
                break
        
        if not original_challenge:
            raise ValueError(f"Challenge {challenge_id} not found")
        
        # Create solution object for analysis
        solution = Solution(**solution_data)
        
        # Enhance the challenge
        enhanced_challenge = await self.enhance_challenge(original_challenge, solution)
        
        # Send enhanced challenge response
        response = MessageFactory.create_challenge_response(
            self.agent_type, message.sender, enhanced_challenge.model_dump()
        )
        await message_bus.publish(response)
    
    async def create_initial_challenge(self, challenge_type: Optional[str] = None,
                                     difficulty: str = "beginner") -> Challenge:
        """Create an initial programming challenge."""
        
        system_prompt = """You are an expert programming challenge creator. Create engaging, well-structured programming problems that test algorithmic thinking and coding skills.

Your response must be valid JSON with the following structure:
{
    "title": "Challenge title",
    "description": "Detailed problem description",
    "challenge_type": "algorithms|data_structures|dynamic_programming|graph_theory|string_manipulation|mathematical_problems",
    "constraints": ["constraint1", "constraint2"],
    "examples": [{"input": "example input", "output": "example output", "explanation": "why this output"}],
    "hints": ["hint1", "hint2"],
    "time_complexity_target": "O(n) or similar",
    "space_complexity_target": "O(1) or similar"
}"""
        
        user_prompt = f"""Create a {difficulty} level programming challenge"""
        if challenge_type:
            user_prompt += f" focused on {challenge_type}"
        
        user_prompt += """. The challenge should be:
1. Clear and unambiguous
2. Have well-defined input/output
3. Include multiple test cases
4. Be solvable but not trivial
5. Test important programming concepts

Make it interesting and educational!"""
        
        messages = [{"role": "user", "content": user_prompt}]
        
        response = llm_client.generate_response_sync(messages, system_prompt)
        challenge_data = llm_client.parse_json_response(response)
        
        # Normalize challenge type (handle cases where LLM returns enum representation)
        challenge_type_str = challenge_data["challenge_type"]
        if challenge_type_str.startswith("ChallengeType."):
            challenge_type_str = challenge_type_str.replace("ChallengeType.", "").lower()

        # Create challenge object
        challenge = Challenge(
            id=str(uuid.uuid4()),
            title=challenge_data["title"],
            description=challenge_data["description"],
            difficulty=DifficultyLevel(difficulty),
            challenge_type=ChallengeType(challenge_type_str),
            constraints=challenge_data.get("constraints", []),
            examples=challenge_data.get("examples", []),
            hints=challenge_data.get("hints", []),
            time_complexity_target=challenge_data.get("time_complexity_target"),
            space_complexity_target=challenge_data.get("space_complexity_target"),
            iteration=1
        )
        
        self.current_challenge = challenge
        self.challenge_history.append(challenge)
        logger.log_challenge_created(challenge)
        
        return challenge
    
    async def enhance_challenge(self, original_challenge: Challenge, 
                              solution: Solution) -> Challenge:
        """Enhance a challenge based on the solution approach."""
        
        system_prompt = """You are an expert at creating progressively harder programming challenges. Given an original challenge and its solution, create a more difficult version that builds upon the same concepts but requires deeper thinking or more advanced techniques.

Your response must be valid JSON with the same structure as the original challenge:
{
    "title": "Enhanced challenge title",
    "description": "Detailed problem description",
    "challenge_type": "same as original",
    "constraints": ["enhanced constraints"],
    "examples": [{"input": "example input", "output": "example output", "explanation": "explanation"}],
    "hints": ["hint1", "hint2"],
    "time_complexity_target": "O(n) or similar",
    "space_complexity_target": "O(1) or similar"
}"""
        
        user_prompt = f"""Original Challenge:
Title: {original_challenge.title}
Description: {original_challenge.description}
Difficulty: {original_challenge.difficulty}
Type: {original_challenge.challenge_type}

Solution Analysis:
Approach: {solution.approach}
Time Complexity: {solution.time_complexity}
Space Complexity: {solution.space_complexity}
Reasoning: {solution.reasoning}

Create an enhanced version that:
1. Builds on the same core concept
2. Increases difficulty appropriately
3. Requires more sophisticated thinking
4. Tests additional edge cases or constraints
5. May combine multiple concepts

The enhanced challenge should be clearly harder but still solvable."""
        
        messages = [{"role": "user", "content": user_prompt}]
        
        response = llm_client.generate_response_sync(messages, system_prompt)
        challenge_data = llm_client.parse_json_response(response)
        
        # Determine new difficulty level
        difficulty_progression = {
            DifficultyLevel.BEGINNER: DifficultyLevel.INTERMEDIATE,
            DifficultyLevel.INTERMEDIATE: DifficultyLevel.ADVANCED,
            DifficultyLevel.ADVANCED: DifficultyLevel.EXPERT,
            DifficultyLevel.EXPERT: DifficultyLevel.EXPERT  # Stay at expert
        }
        
        new_difficulty = difficulty_progression.get(
            original_challenge.difficulty, DifficultyLevel.INTERMEDIATE
        )
        
        # Normalize challenge type (handle cases where LLM returns enum representation)
        challenge_type_str = challenge_data["challenge_type"]
        if challenge_type_str.startswith("ChallengeType."):
            challenge_type_str = challenge_type_str.replace("ChallengeType.", "").lower()

        # Create enhanced challenge
        enhanced_challenge = Challenge(
            id=str(uuid.uuid4()),
            title=challenge_data["title"],
            description=challenge_data["description"],
            difficulty=new_difficulty,
            challenge_type=ChallengeType(challenge_type_str),
            constraints=challenge_data.get("constraints", []),
            examples=challenge_data.get("examples", []),
            hints=challenge_data.get("hints", []),
            time_complexity_target=challenge_data.get("time_complexity_target"),
            space_complexity_target=challenge_data.get("space_complexity_target"),
            iteration=original_challenge.iteration + 1,
            parent_challenge_id=original_challenge.id
        )
        
        self.current_challenge = enhanced_challenge
        self.challenge_history.append(enhanced_challenge)
        logger.log_challenge_created(enhanced_challenge)
        
        return enhanced_challenge
